<template>
  <div class="center-left">
    <div v-for="(item, index) in progressData" :key="index" class="progress-item">
      <div class="top-text">
      <div class="progress-label">
        <img :src="require('../../assets/noNoe.png')" style="width: 42px;height: 22px;margin-right: 10px;" v-if="index==0">
        <img :src="require('../../assets/noTwo.png')" style="width: 42px;height: 22px;margin-right: 10px;" v-if="index==1">
        <img :src="require('../../assets/noThree.png')" style="width: 42px;height: 22px;margin-right: 10px;" v-if="index==2">
        <div style="width: 42px;height: 22px;margin-right: 10px;text-align: left;" v-if="index>2">NO.{{ index+1 }}</div>
        <div >{{ item.label }}</div></div>
        <div :class="index==0?'progress-num':'progress-nums'">12234</div>
      </div>
      <div class="progress-container">
        <div class="progress-bar" :style="getProgressStyle(item.value, index)"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 四个进度条的数据对象
const progressData = ref([
  {
    label: '仓库A',
    value: 85,
  },
  {
    label: '仓库B',
    value: 72,
  },
  {
    label: '仓库C',
    value: 93,
  },
  {
    label: '仓库D',
    value: 68,
  },
  {
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },{
    label: '仓库D',
    value: 68,
  },
])

// 渐变色配置
const gradientColors = [
  'linear-gradient(90deg, #ff6b6b, #ff8e8e)',
  'linear-gradient(90deg, #4ecdc4, #7fdbda)',
  'linear-gradient(90deg, #45b7d1, #74c7ec)',
  'linear-gradient(90deg, #96ceb4, #b8e6d1)',
]

// 获取进度条样式
const getProgressStyle = (value, index) => {
  return {
    width: `${value}%`,
    background: gradientColors[index % gradientColors.length],
    height: '100%',
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20px;
}

.progress-item {
  width: 456px;
  height: 37px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
    .top-text {
    display: flex;
    justify-content: space-between;
    height: 22px;
    align-items: center;
    }
}

.progress-label {
  display: flex;
  align-items: center;
 color: #dff5ec;
 text-align: center;
 font-family: "alia";
 font-size: 14px;
 font-style: normal;
 font-weight: 55 Regular;
 line-height: normal;

}
.progress-num {
 text-align: center;
 font-family: "alia";
 font-size: 16px;
 font-style: normal;
 font-weight: 65 Medium;
 line-height: normal;
 background: linear-gradient(180deg, #FFD699 0%, #FFF 100%);
 background-clip: text;
 -webkit-background-clip: text;
 -webkit-text-fill-color: transparent;
 }

 .progress-nums {
 text-align: center;
 font-family: "alia";
 font-size: 16px;
 font-style: normal;
 font-weight: 65 Medium;
 line-height: normal;
 background: linear-gradient(180deg, #99FFD6 0%, #FFF 100%);
 background-clip: text;
 -webkit-background-clip: text;
 -webkit-text-fill-color: transparent;
 }
.progress-container {
  position: relative;
  width: 456px;
  height: 10px;
  flex-shrink: 0;
  background: #0a3030;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease-in-out;
}
.center-left {
  width: 456px;
  height: 235px;
  overflow: hidden;
  //
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
